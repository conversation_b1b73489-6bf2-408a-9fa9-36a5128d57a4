import type { UserConfig } from 'vite';
import path from 'node:path';
import AutoImport from 'unplugin-auto-import/vite';
import { BossDesignResolver } from '@boss/design-resolver';
import { getEnvConfig } from '../environments';

/**
 * 获取 admin 应用特定配置
 * @param cwd 当前工作目录
 * @param mode 环境模式
 * @returns admin 特定配置
 */
export function getAdminConfig(cwd: string, mode: string): UserConfig {
    const envConfig = getEnvConfig(mode);

    return {
        plugins: [
            // admin 特有的 AutoImport 配置
            AutoImport({
                imports: ['vue', 'vue-router', 'pinia'],
                resolvers: [BossDesignResolver({ autoImport: true })],
                dirs: [
                    path.resolve(cwd, 'src/router-v2'), // 自动导入该路径下所有导出
                ],
            }),
        ],
        css: {
            preprocessorOptions: {
                less: {
                    additionalData: `@import "@/styles/base.less";`,
                },
            },
        },
        server: {
            proxy: {
                '/wapi/cadmin': {
                    target: envConfig.userApiBaseUrl,
                    changeOrigin: true,
                    secure: false,
                },
                '/wapi': {
                    target: envConfig.adminApiBaseUrl,
                    changeOrigin: true,
                },
            },
        },
    };
}
