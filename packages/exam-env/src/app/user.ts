import type { UserConfig } from 'vite';
import { getEnvConfig } from '../environments';

/**
 * 获取 user 应用特定配置
 * @param cwd 当前工作目录
 * @param mode 环境模式
 * @returns user 特定配置
 */
export function getUserConfig(cwd: string, mode: string): UserConfig {
    const envConfig = getEnvConfig(mode);
    const deployVersion = process.env.VITE_DEPLOY_VERSION;

    return {
        define: {
            DEPLOY_VERSION: JSON.stringify(deployVersion),
        },
        css: {
            preprocessorOptions: {
                less: {
                    additionalData: `@import "@/styles/base/index.less";@import "@/styles/base/biz.less";`,
                },
            },
        },
        build: {
            rollupOptions: {
                input: {
                    index: './index.html',
                    login: './login.html',
                },
            },
        },
        server: {
            port: 6688,
            proxy: {
                '/wapi': {
                    target: envConfig.userApiBaseUrl,
                    changeOrigin: true,
                },
            },
            origin: 'http://localhost:6688',
        },
    };
}
